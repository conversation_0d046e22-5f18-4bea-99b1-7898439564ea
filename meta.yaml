{% set name = "ycnbc" %}
{% set version = "1.0.9" %}

package:
  name: "{{ name|lower }}"
  version: "{{ version }}"

source:
  url: "https://pypi.io/packages/source/{{ name[0] }}/{{ name }}/{{ name }}-{{ version }}.tar.gz"
  sha256: "B66FD76CB7706458976358192FAEDA527CEEA264E81CF6EE648C8328B056B165"

build:
  noarch: python
  number: 0
  script: "{{ PYTHON }} -m pip install . --no-deps --ignore-installed -vv "

requirements:
  host:
    - curl-cffi >=0.5.9
    - lxml >=4.5.1
    - cssselect >=1.2.0
    - pip
    - python

  run:
    - curl-cffi >=0.5.9
    - lxml >=4.5.1
    - cssselect >=1.2.0
    - python

test:
  imports:
    - ycnbc

about:
  home: "https://github.com/asepscareer/ycnbc"
  license: "Apache Software License"
  license_family: "APACHE"
  license_file: ""
  summary: "CNBC data downloader"
  description: "A Python package for downloading CNBC data."
  doc_url: "https://github.com/asepscareer/ycnbc"
  dev_url: "https://pypi.python.org/pypi/ycnbc"
  doc_source_url: "https://github.com/asepscareer/ycnbc/blob/master/README.md"

extra:
  recipe-maintainers:
    - asepscareer