name: Publish Python Package

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    environment:
      name: pypi
      url: https://pypi.org/p/ycnbc

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.x'
      - run: pip install build
      - run: python -m build

      - name: GitHub Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: dist/*
          
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
